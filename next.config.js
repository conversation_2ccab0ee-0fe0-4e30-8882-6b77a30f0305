/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode for better development experience
  reactStrictMode: true,

  // Enable image optimization
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com', 'fonts.gstatic.com'],
    formats: ['image/avif', 'image/webp'],
  },

  // Enable experimental features for better performance
  experimental: {
    // Disable optimized CSS to avoid critters issues
    optimizeCss: false,

    // Disable memory-based workers to improve build performance
    // memoryBasedWorkersCount: false, // Disabled - was causing slow builds

    // Enable optimized bundle splitting
    optimizePackageImports: ['lucide-react'],
  },

  // Server external packages (moved from experimental.serverComponentsExternalPackages)
  serverExternalPackages: [],

  // Configure Turbopack for faster development (stable in Next.js 15)
  turbopack: {
    // Enable faster module resolution
    resolveAlias: {
      '@': './src',
    },
  },

  // Configure compiler options
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Remove output mode for faster development builds
  // output: 'standalone', // Disabled for development - can slow down builds

  // Configure page prefetching
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 60 * 60 * 1000, // 1 hour
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 5,
  },

  // Configure headers for better caching (disabled for development)
  // async headers() {
  //   return [
  //     {
  //       source: '/(.*)',
  //       headers: [
  //         {
  //           key: 'Cache-Control',
  //           value: 'public, max-age=31536000, immutable',
  //         },
  //       ],
  //     },
  //   ];
  // },

  // Configure webpack to handle font loading issues
  webpack(config) {
    return config;
  },
};

export default nextConfig;

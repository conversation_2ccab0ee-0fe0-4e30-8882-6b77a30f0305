#!/usr/bin/env node

/**
 * Database Seeding Script
 *
 * This script seeds your Supabase database with sample data for development.
 *
 * Usage:
 * npm run db:seed
 * npm run db:seed --reset (clears existing data first)
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

// Configuration
const isLocal = process.env.SUPABASE_ENV === 'local';
const supabaseUrl = isLocal
  ? 'http://127.0.0.1:54321'
  : process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = isLocal
  ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'
  : process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Sample data
const sampleUsers = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    wallet_address: '******************************************',
    email: '<EMAIL>',
    role: 'investor',
    kyc_status: 'verified'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    wallet_address: '0x8ba1f109551bD432803012645Hac136c30C6756',
    email: '<EMAIL>',
    role: 'property_owner',
    kyc_status: 'verified'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    wallet_address: '******************************************',
    email: '<EMAIL>',
    role: 'admin',
    kyc_status: 'verified'
  }
];

const sampleProperties = [
  {
    id: '660e8400-e29b-41d4-a716-446655440001',
    name: 'Luxury Downtown Apartment',
    description: 'A beautiful 2-bedroom apartment in the heart of downtown',
    price: 500000,
    location: 'Downtown, City Center',
    property_type: 'apartment',
    bedrooms: 2,
    bathrooms: 2,
    square_feet: 1200,
    status: 'active',
    owner_id: '550e8400-e29b-41d4-a716-446655440002',
    images: ['https://images.unsplash.com/photo-1560448204-e02f11c3d0e2']
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440002',
    name: 'Suburban Family Home',
    description: 'Perfect family home with large backyard',
    price: 750000,
    location: 'Suburban Heights',
    property_type: 'house',
    bedrooms: 4,
    bathrooms: 3,
    square_feet: 2500,
    status: 'active',
    owner_id: '550e8400-e29b-41d4-a716-446655440002',
    images: ['https://images.unsplash.com/photo-1570129477492-45c003edd2be']
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440003',
    name: 'Modern Condo with City View',
    description: 'Stunning modern condo with panoramic city views',
    price: 650000,
    location: 'Midtown District',
    property_type: 'condo',
    bedrooms: 3,
    bathrooms: 2,
    square_feet: 1800,
    status: 'active',
    owner_id: '550e8400-e29b-41d4-a716-446655440002',
    images: ['https://images.unsplash.com/photo-1545324418-cc1a3fa10c00']
  }
];

async function clearData() {
  console.log(colorize('🧹 Clearing existing data...', 'yellow'));

  try {
    // Clear in reverse order of dependencies
    await supabase.from('investments').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('properties').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('users').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    console.log(colorize('✅ Data cleared successfully', 'green'));
  } catch (error) {
    console.error(colorize('❌ Error clearing data:', 'red'), error.message);
  }
}

async function seedUsers() {
  console.log(colorize('👥 Seeding users...', 'blue'));

  try {
    const { data, error } = await supabase
      .from('users')
      .insert(sampleUsers)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Inserted ${data.length} users`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error seeding users:', 'red'), error.message);
    return [];
  }
}

async function seedProperties() {
  console.log(colorize('🏠 Seeding properties...', 'blue'));

  try {
    const { data, error } = await supabase
      .from('properties')
      .insert(sampleProperties)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Inserted ${data.length} properties`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error seeding properties:', 'red'), error.message);
    return [];
  }
}

async function seedInvestments(properties) {
  console.log(colorize('💰 Seeding investments...', 'blue'));

  const sampleInvestments = [
    {
      id: '770e8400-e29b-41d4-a716-446655440001',
      property_id: properties[0]?.id,
      investor_id: '550e8400-e29b-41d4-a716-446655440001',
      amount: 50000,
      shares: 10,
      transaction_hash: '0x1234567890abcdef',
      status: 'completed',
      created_at: new Date().toISOString()
    }
  ];

  try {
    const { data, error } = await supabase
      .from('investments')
      .insert(sampleInvestments)
      .select();

    if (error) throw error;

    console.log(colorize(`✅ Inserted ${data.length} investments`, 'green'));
    return data;
  } catch (error) {
    console.error(colorize('❌ Error seeding investments:', 'red'), error.message);
    return [];
  }
}

async function main() {
  const args = process.argv.slice(2);
  const shouldReset = args.includes('--reset');

  console.log(colorize('\n🌱 Database Seeding Script', 'bright'));
  console.log(colorize(`Target: ${isLocal ? 'LOCAL' : 'REMOTE'} database`, 'cyan'));
  console.log('─'.repeat(50));

  try {
    if (shouldReset) {
      await clearData();
    }

    const users = await seedUsers();
    const properties = await seedProperties();
    const investments = await seedInvestments(properties);

    console.log('\n' + colorize('🎉 Seeding completed successfully!', 'green'));
    console.log(colorize(`📊 Summary:`, 'bright'));
    console.log(`   Users: ${users.length}`);
    console.log(`   Properties: ${properties.length}`);
    console.log(`   Investments: ${investments.length}`);

  } catch (error) {
    console.error(colorize('\n❌ Seeding failed:', 'red'), error.message);
    process.exit(1);
  }
}

main();

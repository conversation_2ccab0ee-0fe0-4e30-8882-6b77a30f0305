{"name": "brickchain", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:memory": "NODE_OPTIONS=\"--max-old-space-size=4096\" next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:reset": "npx supabase db reset", "supabase:status": "npx supabase status", "supabase:studio": "npx supabase studio", "db:migrate": "npx supabase db push", "db:generate": "npx supabase gen types typescript --local > src/types/supabase.ts", "db:seed": "node scripts/seed-database.js", "db:sample": "node scripts/add-sample-data.js", "db:simple": "node scripts/add-simple-data.js", "db:query": "node scripts/run-query.js", "db:test": "node scripts/test-local-setup.js", "db:check": "node scripts/check-data.js", "db:raw": "node scripts/check-raw-data.js", "db:users": "node scripts/add-sample-users.js", "db:dev-users": "node scripts/add-dev-users.js", "db:force": "node scripts/force-insert-data.js", "setup:local": "node scripts/setup-local-supabase.js"}, "dependencies": {"@rainbow-me/rainbowkit": "^2.2.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.75.7", "@web3-storage/w3up-client": "^17.2.0", "clsx": "^2.1.1", "critters": "^0.0.23", "ethers": "^5.7.2", "lucide-react": "^0.509.0", "next": "15.3.2", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.34.1", "siwe": "^3.0.0", "tailwind-merge": "^3.3.0", "viem": "^2.29.2", "wagmi": "^2.15.2", "web3.storage": "^4.5.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}
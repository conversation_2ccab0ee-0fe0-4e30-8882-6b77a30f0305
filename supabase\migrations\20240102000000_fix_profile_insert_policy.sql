-- Fix missing INSERT policy for profiles table
-- This allows users to create their own profile during wallet authentication

-- Add INSERT policy for users to create their own profile
CREATE POLICY insert_own_profile ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Also add a policy for admins to insert profiles
CREATE POLICY admin_insert_profiles ON profiles
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Ensure the profiles table has all necessary columns
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS wallet_address TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS kyc_status kyc_status_type DEFAULT 'none';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Make role column nullable (since users will start without a role)
ALTER TABLE profiles ALTER COLUMN role DROP NOT NULL;

-- Create a unique index on wallet_address if it doesn't exist
CREATE UNIQUE INDEX IF NOT EXISTS profiles_wallet_address_idx ON profiles (wallet_address) WHERE wallet_address IS NOT NULL;

-- Update the trigger function if needed
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

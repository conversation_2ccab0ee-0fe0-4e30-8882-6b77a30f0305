#!/usr/bin/env node

/**
 * Test Local Supabase Setup
 * 
 * This script tests that your local Supabase environment is working correctly.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration
const isLocal = process.env.SUPABASE_ENV === 'local';
const supabaseUrl = isLocal 
  ? 'http://127.0.0.1:54321'
  : process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = isLocal
  ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
  : process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function testConnection() {
  console.log(colorize('🔍 Testing database connection...', 'cyan'));
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql: 'SELECT current_database(), version()' 
    });
    
    if (error) throw error;
    
    console.log(colorize('✅ Database connection successful!', 'green'));
    return true;
  } catch (err) {
    console.error(colorize('❌ Database connection failed:', 'red'), err.message);
    return false;
  }
}

async function testTables() {
  console.log(colorize('🔍 Testing table structure...', 'cyan'));
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql: "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'" 
    });
    
    if (error) throw error;
    
    const tables = data.map(row => row.result.table_name);
    const expectedTables = ['users', 'properties', 'investments', 'kyc_documents'];
    const missingTables = expectedTables.filter(table => !tables.includes(table));
    
    if (missingTables.length === 0) {
      console.log(colorize('✅ All required tables exist!', 'green'));
      console.log(colorize(`   Tables: ${tables.join(', ')}`, 'blue'));
      return true;
    } else {
      console.log(colorize('⚠️  Some tables are missing:', 'yellow'));
      console.log(colorize(`   Missing: ${missingTables.join(', ')}`, 'red'));
      return false;
    }
  } catch (err) {
    console.error(colorize('❌ Table check failed:', 'red'), err.message);
    return false;
  }
}

async function showServices() {
  console.log(colorize('\n🌐 Local Supabase Services:', 'bright'));
  console.log(colorize('   API URL: http://127.0.0.1:54321', 'blue'));
  console.log(colorize('   Studio: http://127.0.0.1:54323', 'blue'));
  console.log(colorize('   Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres', 'blue'));
}

async function showCommands() {
  console.log(colorize('\n🛠️  Available Commands:', 'bright'));
  console.log(colorize('   npm run db:query "SELECT * FROM users"', 'cyan'));
  console.log(colorize('   npm run db:query --interactive', 'cyan'));
  console.log(colorize('   npm run supabase:studio', 'cyan'));
  console.log(colorize('   npm run supabase:status', 'cyan'));
  console.log(colorize('   node scripts/switch-env.js remote', 'cyan'));
}

async function main() {
  console.log(colorize('🧪 Local Supabase Setup Test', 'bright'));
  console.log(colorize(`Environment: ${isLocal ? 'LOCAL' : 'REMOTE'}`, isLocal ? 'green' : 'yellow'));
  console.log('─'.repeat(50));
  
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.log(colorize('\n💡 Make sure Supabase is running: npm run supabase:start', 'yellow'));
    return;
  }
  
  const tablesOk = await testTables();
  
  if (connectionOk && tablesOk) {
    console.log(colorize('\n🎉 Local Supabase setup is working perfectly!', 'green'));
    showServices();
    showCommands();
    
    console.log(colorize('\n📚 Next Steps:', 'bright'));
    console.log('1. Open Supabase Studio: http://127.0.0.1:54323');
    console.log('2. Start your development server: npm run dev');
    console.log('3. Your app will now use the local Supabase instance');
    
    console.log(colorize('\n✨ You can now develop without going to the browser constantly!', 'green'));
  } else {
    console.log(colorize('\n⚠️  Setup needs attention. Check the errors above.', 'yellow'));
  }
}

main().catch(console.error);

-- BrickChain Database Schema
-- Complete schema setup for wallet-based authentication and property management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role_type') THEN
    CREATE TYPE user_role_type AS ENUM ('investor', 'property_owner', 'admin');
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'kyc_status_type') THEN
    CREATE TYPE kyc_status_type AS ENUM ('none', 'pending', 'verified', 'rejected');
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'property_status_type') THEN
    CREATE TYPE property_status_type AS ENUM ('draft', 'active', 'sold', 'inactive');
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'investment_status_type') THEN
    CREATE TYPE investment_status_type AS ENUM ('pending', 'completed', 'failed', 'cancelled');
  END IF;
END $$;

-- Create users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_address TEXT UNIQUE,
  email TEXT,
  role user_role_type,
  kyc_status kyc_status_type DEFAULT 'none',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create properties table
CREATE TABLE IF NOT EXISTS public.properties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(15,2) NOT NULL,
  location TEXT NOT NULL,
  property_type TEXT NOT NULL,
  bedrooms INTEGER,
  bathrooms INTEGER,
  square_feet INTEGER,
  status property_status_type DEFAULT 'draft',
  owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  images TEXT[], -- Array of image URLs
  documents TEXT[], -- Array of document URLs
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create investments table
CREATE TABLE IF NOT EXISTS public.investments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  investor_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  amount DECIMAL(15,2) NOT NULL,
  shares INTEGER NOT NULL DEFAULT 1,
  transaction_hash TEXT,
  status investment_status_type DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update the updated_at column
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_properties_updated_at ON public.properties;
CREATE TRIGGER update_properties_updated_at
BEFORE UPDATE ON public.properties
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_investments_updated_at ON public.investments;
CREATE TRIGGER update_investments_updated_at
BEFORE UPDATE ON public.investments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create KYC documents table if it doesn't exist
CREATE TABLE IF NOT EXISTS kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL,
  document_category TEXT NOT NULL, -- 'identity', 'address', 'financial'
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  verification_status kyc_status_type DEFAULT 'pending',
  verification_notes TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create a trigger to update the updated_at column for kyc_documents
DROP TRIGGER IF EXISTS update_kyc_documents_updated_at ON kyc_documents;
CREATE TRIGGER update_kyc_documents_updated_at
BEFORE UPDATE ON kyc_documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create RLS policies for users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own profile
CREATE POLICY read_own_profile ON public.users
  FOR SELECT
  USING (auth.uid() = id);

-- Policy for users to update their own profile
CREATE POLICY update_own_profile ON public.users
  FOR UPDATE
  USING (auth.uid() = id);

-- Policy for users to insert their own profile
CREATE POLICY insert_own_profile ON public.users
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Policy for admins to read all profiles
CREATE POLICY admin_read_all_profiles ON public.users
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for admins to update all profiles
CREATE POLICY admin_update_all_profiles ON public.users
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create RLS policies for properties table
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;

-- Policy for anyone to read active properties
CREATE POLICY read_active_properties ON public.properties
  FOR SELECT
  USING (status = 'active');

-- Policy for owners to read their own properties
CREATE POLICY read_own_properties ON public.properties
  FOR SELECT
  USING (auth.uid() = owner_id);

-- Policy for owners to insert their own properties
CREATE POLICY insert_own_properties ON public.properties
  FOR INSERT
  WITH CHECK (auth.uid() = owner_id);

-- Policy for owners to update their own properties
CREATE POLICY update_own_properties ON public.properties
  FOR UPDATE
  USING (auth.uid() = owner_id);

-- Policy for admins to read all properties
CREATE POLICY admin_read_all_properties ON public.properties
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create RLS policies for investments table
ALTER TABLE public.investments ENABLE ROW LEVEL SECURITY;

-- Policy for investors to read their own investments
CREATE POLICY read_own_investments ON public.investments
  FOR SELECT
  USING (auth.uid() = investor_id);

-- Policy for property owners to read investments in their properties
CREATE POLICY read_property_investments ON public.investments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.properties
      WHERE id = property_id AND owner_id = auth.uid()
    )
  );

-- Policy for investors to insert their own investments
CREATE POLICY insert_own_investments ON public.investments
  FOR INSERT
  WITH CHECK (auth.uid() = investor_id);

-- Policy for admins to read all investments
CREATE POLICY admin_read_all_investments ON public.investments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create RLS policies for kyc_documents table
ALTER TABLE kyc_documents ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own documents
CREATE POLICY read_own_documents ON kyc_documents
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy for users to insert their own documents
CREATE POLICY insert_own_documents ON kyc_documents
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for admins to read all documents
CREATE POLICY admin_read_all_documents ON kyc_documents
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for admins to update all documents
CREATE POLICY admin_update_all_documents ON kyc_documents
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS users_wallet_address_idx ON public.users (wallet_address);
CREATE INDEX IF NOT EXISTS users_role_idx ON public.users (role);
CREATE INDEX IF NOT EXISTS users_kyc_status_idx ON public.users (kyc_status);
CREATE INDEX IF NOT EXISTS properties_owner_id_idx ON public.properties (owner_id);
CREATE INDEX IF NOT EXISTS properties_status_idx ON public.properties (status);
CREATE INDEX IF NOT EXISTS investments_property_id_idx ON public.investments (property_id);
CREATE INDEX IF NOT EXISTS investments_investor_id_idx ON public.investments (investor_id);
CREATE INDEX IF NOT EXISTS investments_status_idx ON public.investments (status);
CREATE INDEX IF NOT EXISTS kyc_documents_user_id_idx ON kyc_documents (user_id);

-- Create a function to handle user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email)
  VALUES (NEW.id, NEW.email);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create a simple function to execute SQL (for development/testing)
CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
RETURNS TABLE(result JSONB) AS $$
BEGIN
  RETURN QUERY EXECUTE 'SELECT to_jsonb(t) FROM (' || sql || ') t';
EXCEPTION
  WHEN OTHERS THEN
    RETURN QUERY SELECT to_jsonb(jsonb_build_object('error', SQLERRM));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

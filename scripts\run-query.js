#!/usr/bin/env node

/**
 * Database Query Runner
 *
 * This script allows you to run SQL queries against your Supabase database
 * from the command line without going to the browser.
 *
 * Usage:
 * npm run db:query "SELECT * FROM users LIMIT 5"
 * npm run db:query --file queries/get-properties.sql
 * npm run db:query --interactive
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import readline from 'readline';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Configuration
const isLocal = process.env.SUPABASE_ENV === 'local';
const supabaseUrl = isLocal
  ? 'http://127.0.0.1:54321'
  : process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = isLocal
  ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
  : process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function formatTable(data) {
  if (!data || data.length === 0) {
    return 'No results found.';
  }

  const keys = Object.keys(data[0]);
  const maxLengths = keys.map(key =>
    Math.max(key.length, ...data.map(row => String(row[key] || '').length))
  );

  // Header
  let table = '┌' + maxLengths.map(len => '─'.repeat(len + 2)).join('┬') + '┐\n';
  table += '│' + keys.map((key, i) => ` ${key.padEnd(maxLengths[i])} `).join('│') + '│\n';
  table += '├' + maxLengths.map(len => '─'.repeat(len + 2)).join('┼') + '┤\n';

  // Rows
  data.forEach(row => {
    table += '│' + keys.map((key, i) =>
      ` ${String(row[key] || '').padEnd(maxLengths[i])} `
    ).join('│') + '│\n';
  });

  table += '└' + maxLengths.map(len => '─'.repeat(len + 2)).join('┴') + '┘';
  return table;
}

async function runQuery(query) {
  console.log(colorize(`\n🔍 Running query against ${isLocal ? 'LOCAL' : 'REMOTE'} database...`, 'cyan'));
  console.log(colorize(`Query: ${query}`, 'yellow'));
  console.log('─'.repeat(80));

  try {
    const startTime = Date.now();
    let data, error;

    // For SELECT queries, use the from() method
    if (query.trim().toLowerCase().startsWith('select')) {
      // Try to parse simple SELECT queries
      const trimmedQuery = query.trim().toLowerCase();

      if (trimmedQuery.includes('from users')) {
        const result = await supabase.from('users').select('*');
        data = result.data;
        error = result.error;
      } else if (trimmedQuery.includes('from profiles')) {
        const result = await supabase.from('profiles').select('*');
        data = result.data;
        error = result.error;
      } else if (trimmedQuery.includes('from properties')) {
        const result = await supabase.from('properties').select('*');
        data = result.data;
        error = result.error;
      } else if (trimmedQuery.includes('from investments')) {
        const result = await supabase.from('investments').select('*');
        data = result.data;
        error = result.error;
      } else {
        // For complex queries, try the RPC function if available
        try {
          const result = await supabase.rpc('exec_sql', { sql: query });
          data = result.data;
          error = result.error;

          if (data && !error) {
            data = data.map(row => row.result).filter(row => !row.error);
          }
        } catch (rpcError) {
          console.log(colorize('⚠️  Complex queries not supported on remote database without exec_sql function', 'yellow'));
          console.log(colorize('💡 Try using simple table queries like: SELECT * FROM users', 'cyan'));
          return;
        }
      }
    } else {
      console.log(colorize('⚠️  Non-SELECT queries not supported in remote mode', 'yellow'));
      console.log(colorize('💡 Use Supabase Studio for DDL operations: https://supabase.com/dashboard', 'cyan'));
      return;
    }

    const endTime = Date.now();

    if (error) {
      console.error(colorize('❌ Error:', 'red'), error.message);
      return;
    }

    console.log(colorize(`✅ Query executed successfully in ${endTime - startTime}ms`, 'green'));

    if (data && data.length > 0) {
      console.log(colorize('\n📊 Results:', 'blue'));
      console.log(formatTable(data));
      console.log(colorize(`\n📈 ${data.length} row(s) returned`, 'green'));
    } else {
      console.log(colorize('\n✨ Query executed successfully (no results returned)', 'green'));
    }

  } catch (err) {
    console.error(colorize('❌ Unexpected error:', 'red'), err.message);
  }
}

async function interactiveMode() {
  console.log(colorize('\n🚀 Interactive SQL Mode', 'bright'));
  console.log(colorize(`Connected to ${isLocal ? 'LOCAL' : 'REMOTE'} database`, 'cyan'));
  console.log(colorize('Type your SQL queries and press Enter. Type "exit" to quit.\n', 'yellow'));

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: colorize('sql> ', 'magenta')
  });

  rl.prompt();

  rl.on('line', async (line) => {
    const query = line.trim();

    if (query.toLowerCase() === 'exit') {
      console.log(colorize('👋 Goodbye!', 'green'));
      rl.close();
      return;
    }

    if (query) {
      await runQuery(query);
    }

    rl.prompt();
  });

  rl.on('close', () => {
    process.exit(0);
  });
}

async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--interactive') || args.includes('-i')) {
    await interactiveMode();
    return;
  }

  const fileIndex = args.indexOf('--file');
  if (fileIndex !== -1 && args[fileIndex + 1]) {
    const filePath = args[fileIndex + 1];
    try {
      const query = fs.readFileSync(path.resolve(filePath), 'utf8');
      await runQuery(query);
    } catch (err) {
      console.error(colorize('❌ Error reading file:', 'red'), err.message);
    }
    return;
  }

  const query = args.join(' ');
  if (query) {
    await runQuery(query);
  } else {
    console.log(colorize('📖 Usage:', 'bright'));
    console.log('  npm run db:query "SELECT * FROM users LIMIT 5"');
    console.log('  npm run db:query --file queries/get-properties.sql');
    console.log('  npm run db:query --interactive');
  }
}

main().catch(console.error);
